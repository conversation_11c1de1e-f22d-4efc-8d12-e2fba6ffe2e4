/**
 * Comprehensive Product Description Preservation Test Suite
 *
 * This consolidated test script validates:
 * - 100% preservation of user-provided additional information
 * - Technical specifications and complex data preservation
 * - Advanced diversity features and structural variation
 * - Critical business information integrity
 *
 * Consolidated from: test-preservation-validation.js, test-enhanced-preservation.js, test-advanced-diversity.js
 *
 * Usage: node test-preservation-validation.js
 * Note: Requires OPENAI_API_KEY environment variable to be set
 */

// Enhanced technical test cases for complex specifications
const enhancedTechnicalCases = [
  {
    name: "Ração Premium com Informações Técnicas Complexas",
    category: "Animais",
    features: ["Sem cereais", "Rico em proteínas", "Digestão fácil"],
    keywords: ["ração cães premium", "alimentação canina"],
    targetAudience: "Donos de cães de raça",
    additionalInfo: "Composição: Proteína bruta 32%, Gordura bruta 18%, Fibra bruta 3.5%, Cinza bruta 7.2%, Humidade 10%. Ingredientes principais: Carne de frango desidrata<PERSON> (28%), batata do<PERSON>, er<PERSON><PERSON>, gord<PERSON> de frango, <PERSON><PERSON> (4%), glucosamina (1000mg/kg), condroitina (800mg/kg). Tecnologia de extrusão dupla a baixa temperatura preserva nutrientes. Aprovado pela FEDIAF. Sem conservantes artificiais, corantes ou aromatizantes. Enriquecido com probióticos Lactobacillus acidophilus (1x10^9 UFC/kg). Adequado para cães adultos de todas as raças.",
    criticalElements: [
      "Proteína bruta 32%", "Gordura bruta 18%", "Fibra bruta 3.5%", "Cinza bruta 7.2%", "Humidade 10%",
      "Carne de frango desidratada (28%)", "óleo de salmão (4%)", "glucosamina (1000mg/kg)", "condroitina (800mg/kg)",
      "extrusão dupla", "baixa temperatura", "FEDIAF", "Lactobacillus acidophilus", "1x10^9 UFC/kg"
    ]
  },
  {
    name: "Smartphone com Especificações Técnicas Avançadas",
    category: "Tecnologia",
    features: ["5G", "Câmara tripla", "Ecrã OLED"],
    keywords: ["smartphone premium", "telemóvel 5g"],
    targetAudience: "Profissionais e entusiastas",
    additionalInfo: "Processador Snapdragon 8 Gen 2 (4nm), RAM 12GB LPDDR5X, Armazenamento 256GB UFS 4.0. Ecrã AMOLED 6.8\" 3200x1440px, 120Hz, 1300 nits, Gorilla Glass Victus 2. Sistema de câmaras: Principal 50MP f/1.8 OIS, Ultra-wide 12MP f/2.2 120°, Teleobjetiva 10MP f/2.4 3x zoom ótico. Bateria 5000mAh, carregamento rápido 67W, carregamento sem fios 50W, carregamento reverso 10W. Resistência IP68, conectividade Wi-Fi 7, Bluetooth 5.3, NFC, USB-C 3.2. Certificação militar MIL-STD-810H.",
    criticalElements: [
      "Snapdragon 8 Gen 2 (4nm)", "RAM 12GB LPDDR5X", "256GB UFS 4.0", "AMOLED 6.8\"", "3200x1440px",
      "120Hz", "1300 nits", "Gorilla Glass Victus 2", "50MP f/1.8 OIS", "12MP f/2.2 120°", "10MP f/2.4 3x zoom",
      "5000mAh", "67W", "50W", "10W", "IP68", "Wi-Fi 7", "Bluetooth 5.3", "USB-C 3.2", "MIL-STD-810H"
    ]
  }
];

// Critical business information test cases
const criticalTestCases = [
  {
    name: "Smartphone Premium com Informações Críticas",
    category: "Tecnologia",
    features: ["Ecrã OLED", "5G", "Câmara 108MP"],
    keywords: ["smartphone premium", "telemóvel 5g"],
    targetAudience: "Profissionais",
    additionalInfo: "Edição limitada de apenas 500 unidades numeradas. Inclui carregador sem fios de 65W, capa de couro italiana artesanal e auriculares Bluetooth premium. Garantia estendida de 3 anos com suporte técnico 24/7. Certificado pela União Europeia para resistência à água IP68. Processador exclusivo desenvolvido em parceria com a TSMC.",
    criticalElements: [
      "Edição limitada de apenas 500 unidades numeradas",
      "carregador sem fios de 65W",
      "capa de couro italiana artesanal",
      "auriculares Bluetooth premium",
      "Garantia estendida de 3 anos",
      "suporte técnico 24/7",
      "Certificado pela União Europeia",
      "resistência à água IP68",
      "Processador exclusivo",
      "parceria com a TSMC"
    ]
  },
  {
    name: "Azeite Artesanal com Certificações",
    category: "Alimentação",
    features: ["Extra virgem", "Primeira extração", "Acidez 0.1%"],
    keywords: ["azeite português", "azeite premium"],
    targetAudience: "Chefs e gourmets",
    additionalInfo: "Produzido em olivais centenários da região de Trás-os-Montes com mais de 300 anos. Prémio Ouro no Concurso Internacional de Azeites de Madrid 2024. Colheita manual realizada exclusivamente durante o mês de outubro. Prensagem a frio em menos de 6 horas após a colheita. Engarrafado em ambiente controlado com azoto para preservar as propriedades. Certificação DOP (Denominação de Origem Protegida) e certificação biológica pela ECOCERT.",
    criticalElements: [
      "olivais centenários",
      "região de Trás-os-Montes",
      "mais de 300 anos",
      "Prémio Ouro",
      "Concurso Internacional de Azeites de Madrid 2024",
      "Colheita manual",
      "mês de outubro",
      "Prensagem a frio",
      "menos de 6 horas",
      "Engarrafado em ambiente controlado",
      "azoto",
      "Certificação DOP",
      "certificação biológica",
      "ECOCERT"
    ]
  },
  {
    name: "Ração Premium para Cães com Especificações Veterinárias",
    category: "Animais",
    features: ["Sem cereais", "Rico em proteínas", "Digestão fácil"],
    keywords: ["ração cães premium", "alimentação canina"],
    targetAudience: "Donos de cães de raça",
    additionalInfo: "Fórmula desenvolvida por veterinários da Universidade de Lisboa em colaboração com nutricionistas caninos certificados. Testada clinicamente em mais de 200 cães durante 18 meses. Aprovada pela Associação Portuguesa de Medicina Veterinária (APMV). Ingredientes 100% naturais provenientes de fornecedores certificados pela UE. Sem conservantes artificiais, corantes ou aromatizantes sintéticos. Embalagem com atmosfera modificada para preservar a frescura. Análises microbiológicas realizadas em cada lote.",
    criticalElements: [
      "veterinários da Universidade de Lisboa",
      "nutricionistas caninos certificados",
      "Testada clinicamente",
      "mais de 200 cães",
      "18 meses",
      "Associação Portuguesa de Medicina Veterinária",
      "APMV",
      "Ingredientes 100% naturais",
      "fornecedores certificados pela UE",
      "Sem conservantes artificiais",
      "corantes ou aromatizantes sintéticos",
      "atmosfera modificada",
      "Análises microbiológicas",
      "cada lote"
    ]
  }
];

async function testPreservationValidation() {
  console.log("🔍 COMPREHENSIVE PRESERVATION TEST SUITE");
  console.log("=" .repeat(80));

  if (!process.env.OPENAI_API_KEY) {
    console.error("❌ Erro: OPENAI_API_KEY não está definida nas variáveis de ambiente");
    return;
  }

  console.log("✅ Chave da OpenAI encontrada");

  // Combine all test cases
  const allTestCases = [...enhancedTechnicalCases, ...criticalTestCases];
  console.log(`📝 A testar ${allTestCases.length} casos (${enhancedTechnicalCases.length} técnicos + ${criticalTestCases.length} críticos)...\n`);

  const results = [];
  let totalFailures = 0;

  for (let i = 0; i < allTestCases.length; i++) {
    const testCase = allTestCases[i];
    console.log(`🧪 Teste ${i + 1}/${allTestCases.length}: ${testCase.name}`);
    console.log(`📋 Informações adicionais: ${testCase.additionalInfo.length} caracteres`);
    console.log(`🎯 Elementos críticos a preservar: ${testCase.criticalElements.length}`);
    
    try {
      const response = await fetch('http://localhost:3000/api/generate-description', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'generate',
          productInfo: testCase
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      const description = data.seoContent.wooCommerceMainDescription;
      
      // Validação detalhada de cada elemento crítico
      const descriptionLower = description.toLowerCase();
      const preservedElements = [];
      const missingElements = [];
      
      for (const element of testCase.criticalElements) {
        const elementWords = element.toLowerCase().split(/\s+/).filter(word => word.length > 2);
        const matchedWords = elementWords.filter(word => descriptionLower.includes(word));
        const matchRate = matchedWords.length / elementWords.length;
        
        if (matchRate >= 0.7) { // 70% das palavras do elemento devem estar presentes
          preservedElements.push(element);
        } else {
          missingElements.push(element);
        }
      }
      
      const preservationRate = (preservedElements.length / testCase.criticalElements.length) * 100;
      const isSuccess = preservationRate >= 90;
      
      console.log(`📊 Resultados:`);
      console.log(`   Taxa de preservação: ${preservationRate.toFixed(1)}%`);
      console.log(`   Elementos preservados: ${preservedElements.length}/${testCase.criticalElements.length}`);
      console.log(`   Elementos em falta: ${missingElements.length}`);
      
      if (isSuccess) {
        console.log(`   ✅ SUCESSO: Preservação adequada`);
      } else {
        console.log(`   ❌ FALHA: Preservação insuficiente`);
        totalFailures++;
      }
      
      if (preservedElements.length > 0) {
        console.log(`   ✅ Preservados: ${preservedElements.slice(0, 3).join(', ')}${preservedElements.length > 3 ? '...' : ''}`);
      }
      
      if (missingElements.length > 0) {
        console.log(`   ❌ Em falta: ${missingElements.slice(0, 3).join(', ')}${missingElements.length > 3 ? '...' : ''}`);
      }
      
      results.push({
        testCase: testCase.name,
        preservationRate,
        preservedCount: preservedElements.length,
        missingCount: missingElements.length,
        isSuccess,
        description: description.substring(0, 200) + "..."
      });
      
      console.log("-".repeat(80));
      
    } catch (error) {
      console.error(`❌ Erro no teste ${testCase.name}:`, error.message);
      totalFailures++;
      console.log("-".repeat(80));
    }
    
    // Delay para evitar rate limiting
    if (i < criticalTestCases.length - 1) {
      console.log("⏳ Aguardando 3 segundos...\n");
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
  }

  // Relatório final
  console.log("\n📋 RELATÓRIO FINAL DE PRESERVAÇÃO");
  console.log("=" .repeat(80));
  
  const successRate = ((results.length - totalFailures) / results.length) * 100;
  const avgPreservationRate = results.reduce((sum, r) => sum + r.preservationRate, 0) / results.length;
  
  console.log(`📊 Estatísticas Gerais:`);
  console.log(`   Taxa de sucesso dos testes: ${successRate.toFixed(1)}%`);
  console.log(`   Taxa média de preservação: ${avgPreservationRate.toFixed(1)}%`);
  console.log(`   Testes com falha: ${totalFailures}/${results.length}`);
  
  console.log(`\n🎯 Resultados por Teste:`);
  results.forEach((result, index) => {
    const status = result.isSuccess ? '✅' : '❌';
    console.log(`   ${status} ${result.testCase}: ${result.preservationRate.toFixed(1)}% (${result.preservedCount}/${result.preservedCount + result.missingCount})`);
  });
  
  console.log(`\n📋 Recomendações:`);
  if (successRate < 100) {
    console.log("   ⚠️  CRÍTICO: Alguns testes falharam - revisar prompts de preservação");
  }
  if (avgPreservationRate < 90) {
    console.log("   ⚠️  CRÍTICO: Taxa de preservação abaixo do mínimo exigido (90%)");
  }
  if (totalFailures === 0 && avgPreservationRate >= 95) {
    console.log("   ✅ EXCELENTE: Todos os testes passaram com preservação superior a 95%");
  }
  
  return {
    successRate,
    avgPreservationRate,
    totalFailures,
    results
  };
}

// Executar o teste
if (require.main === module) {
  console.log("🚀 Iniciando teste abrangente de preservação...\n");
  testPreservationValidation().catch(console.error);
}

module.exports = {
  testPreservationValidation,
  enhancedTechnicalCases,
  criticalTestCases
};

module.exports = { testPreservationValidation, criticalTestCases };
