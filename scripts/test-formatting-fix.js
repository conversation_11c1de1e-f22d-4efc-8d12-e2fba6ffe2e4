#!/usr/bin/env node

/**
 * Test Script for Formatting Fix Validation
 * Tests that AI-generated content uses only bold formatting and no underlines/italics
 */

const { config } = require('dotenv');
config({ path: '.env.local' });

// Test products to validate formatting
const testProducts = [
  {
    name: "Smartphone Premium 5G",
    category: "Tecnologia",
    features: ["Ecrã OLED 6.8\"", "Câmara tripla 108MP", "Bateria 5000mAh", "Processador octa-core"],
    keywords: ["smartphone premium", "telemóvel 5G", "câmara profissional"],
    targetAudience: "Utilizadores exigentes",
    additionalInfo: "Processador Snapdragon 8 Gen 3 (3.2GHz), 12GB RAM LPDDR5, 512GB UFS 4.0. Ecrã Dynamic AMOLED 2X com taxa de atualização 120Hz. Sistema de câmaras com sensor principal Samsung ISOCELL HM3 de 108MP, ultra-wide 12MP e teleobjetiva 10MP com zoom ótico 3x. Bateria de 5000mAh com carregamento rápido 45W e wireless 15W."
  },
  {
    name: "Azeite Extra Virgem Biológico",
    category: "Alimentação",
    features: ["Primeira extração", "Certificação biológica", "Acidez 0.2%", "Origem controlada"],
    keywords: ["azeite biológico", "azeite português", "extra virgem premium"],
    targetAudience: "Amantes de gastronomia",
    additionalInfo: "Produzido em olivais centenários da região do Douro. Azeitonas colhidas à mão e prensadas a frio em menos de 6 horas. Rico em polifenóis (280mg/kg) e vitamina E. Certificação biológica EU e DOP Douro. Acidez inferior a 0.2%. Prémio Ouro no Concurso Nacional de Azeites 2024."
  }
];

/**
 * Validate that content has proper formatting
 */
function validateFormatting(content, fieldName) {
  const issues = [];
  
  // Check for unwanted underline formatting
  if (content.includes('<u>') || content.includes('</u>')) {
    issues.push(`${fieldName}: Contains unwanted underline tags (<u>)`);
  }
  
  // Check for unwanted italic formatting
  if (content.includes('<em>') || content.includes('</em>') || 
      content.includes('<i>') || content.includes('</i>')) {
    issues.push(`${fieldName}: Contains unwanted italic tags (<em> or <i>)`);
  }
  
  // Check for markdown italic formatting
  if (content.match(/\*[^*]+\*/) && !content.match(/\*\*[^*]+\*\*/)) {
    issues.push(`${fieldName}: Contains unwanted markdown italic (*text*)`);
  }
  
  if (content.match(/_[^_]+_/) && !content.match(/__[^_]+__/)) {
    issues.push(`${fieldName}: Contains unwanted markdown italic (_text_)`);
  }
  
  // Check for proper bold formatting
  const hasBoldTags = content.includes('<strong>') && content.includes('</strong>');
  const hasBoldMarkdown = content.includes('**');
  
  if (!hasBoldTags && !hasBoldMarkdown) {
    issues.push(`${fieldName}: Missing bold formatting - should use <strong> tags for emphasis`);
  }
  
  // Check for balanced tags
  const strongOpenCount = (content.match(/<strong>/g) || []).length;
  const strongCloseCount = (content.match(/<\/strong>/g) || []).length;
  
  if (strongOpenCount !== strongCloseCount) {
    issues.push(`${fieldName}: Unbalanced <strong> tags (${strongOpenCount} open, ${strongCloseCount} close)`);
  }
  
  return issues;
}

/**
 * Analyze content quality and formatting
 */
function analyzeContent(seoContent, productName) {
  const analysis = {
    product: productName,
    formatIssues: [],
    qualityMetrics: {},
    success: true
  };
  
  // Validate formatting for each field
  const fields = [
    { name: 'wooCommerceMainDescription', content: seoContent.wooCommerceMainDescription },
    { name: 'wooCommerceShortDescription', content: seoContent.wooCommerceShortDescription },
    { name: 'shortDescription', content: seoContent.shortDescription }
  ];
  
  fields.forEach(field => {
    const issues = validateFormatting(field.content, field.name);
    analysis.formatIssues.push(...issues);
  });
  
  // Quality metrics
  analysis.qualityMetrics = {
    seoLength: seoContent.shortDescription.length,
    seoValid: seoContent.shortDescription.length >= 140 && seoContent.shortDescription.length <= 160,
    hasPortugueseChars: /[àáâãçéêíóôõú]/i.test(seoContent.wooCommerceMainDescription),
    strongTagsCount: (seoContent.wooCommerceMainDescription.match(/<strong>/g) || []).length,
    mainDescLength: seoContent.wooCommerceMainDescription.length
  };
  
  analysis.success = analysis.formatIssues.length === 0;
  
  return analysis;
}

async function testFormattingFix() {
  console.log("🎨 Teste de Correção de Formatação");
  console.log("=" .repeat(60));
  
  if (!process.env.OPENAI_API_KEY) {
    console.error("❌ Erro: OPENAI_API_KEY não está definida");
    console.log("💡 Configure a variável no arquivo .env.local");
    return;
  }

  console.log("✅ Configuração encontrada");
  console.log(`📝 Testando formatação em ${testProducts.length} produtos...\n`);

  const results = [];
  let successCount = 0;

  for (let i = 0; i < testProducts.length; i++) {
    const product = testProducts[i];
    console.log(`🔍 Teste ${i + 1}/${testProducts.length}: ${product.name}`);
    
    try {
      const startTime = Date.now();
      
      const response = await fetch('http://localhost:3000/api/generate-description', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'generate',
          productInfo: product
        }),
      });

      const endTime = Date.now();
      const responseTime = endTime - startTime;

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP ${response.status}`);
      }

      const data = await response.json();
      const seoContent = data.seoContent;

      // Analyze formatting and quality
      const analysis = analyzeContent(seoContent, product.name);
      analysis.responseTime = responseTime;
      
      results.push(analysis);

      if (analysis.success) {
        successCount++;
        console.log(`✅ Formatação correta em ${responseTime}ms`);
      } else {
        console.log(`❌ Problemas de formatação encontrados:`);
        analysis.formatIssues.forEach(issue => {
          console.log(`   - ${issue}`);
        });
      }
      
      // Display quality metrics
      console.log(`📏 SEO: ${analysis.qualityMetrics.seoLength} chars ${analysis.qualityMetrics.seoValid ? '✅' : '❌'}`);
      console.log(`🇵🇹 Português: ${analysis.qualityMetrics.hasPortugueseChars ? '✅' : '❌'}`);
      console.log(`💪 Tags <strong>: ${analysis.qualityMetrics.strongTagsCount}`);
      console.log("");

    } catch (error) {
      console.error(`❌ Falha: ${error.message}`);
      results.push({
        product: product.name,
        success: false,
        error: error.message
      });
      console.log("");
    }
  }

  // Summary
  console.log("📊 RESUMO DOS TESTES DE FORMATAÇÃO");
  console.log("=" .repeat(60));
  console.log(`✅ Formatação correta: ${successCount}/${testProducts.length}`);
  
  const totalIssues = results.reduce((acc, r) => acc + (r.formatIssues?.length || 0), 0);
  console.log(`🎨 Total de problemas de formatação: ${totalIssues}`);
  
  if (totalIssues > 0) {
    console.log("\n🔧 PROBLEMAS ENCONTRADOS:");
    results.forEach(result => {
      if (result.formatIssues && result.formatIssues.length > 0) {
        console.log(`\n📦 ${result.product}:`);
        result.formatIssues.forEach(issue => {
          console.log(`   - ${issue}`);
        });
      }
    });
  }

  const avgStrongTags = results
    .filter(r => r.qualityMetrics)
    .reduce((acc, r) => acc + r.qualityMetrics.strongTagsCount, 0) / successCount || 0;
  
  console.log(`💪 Média de tags <strong> por descrição: ${avgStrongTags.toFixed(1)}`);

  if (successCount === testProducts.length && totalIssues === 0) {
    console.log("\n🎉 TODOS OS TESTES DE FORMATAÇÃO PASSARAM!");
    console.log("✅ A correção de formatação foi bem-sucedida!");
    console.log("🎨 Apenas formatação em negrito (<strong>) está sendo usada");
  } else {
    console.log("\n⚠️  PROBLEMAS DE FORMATAÇÃO DETECTADOS");
    console.log("🔧 Verifique os problemas listados acima");
  }

  return results;
}

// Run tests if called directly
if (require.main === module) {
  testFormattingFix().catch(console.error);
}

module.exports = { testFormattingFix };
