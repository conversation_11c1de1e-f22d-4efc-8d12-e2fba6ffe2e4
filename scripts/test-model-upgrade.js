#!/usr/bin/env node

/**
 * Test Script for OpenAI Model Upgrade
 * Tests the new GPT-4.1 implementation with Portuguese product descriptions
 */

const { config } = require('dotenv');
config({ path: '.env.local' });

// Test cases for Portuguese e-commerce products
const testProducts = [
  {
    name: "Azeite Extra Virgem Português",
    category: "Alimentação",
    features: ["Primeira extração a frio", "Acidez baixa 0.2%", "Certificação DOP"],
    keywords: ["azeite português premium", "azeite extra virgem", "azeite DOP"],
    targetAudience: "Chefs e amantes de gastronomia",
    additionalInfo: "Produzido em olivais centenários da região de Trás-os-Montes. Prémio Ouro no Concurso Internacional de Azeites 2024. Rico em antioxidantes naturais (polifenóis 250mg/kg). Certificação biológica e DOP Trás-os-Montes."
  },
  {
    name: "Smartphone Android 5G",
    category: "Tecnologia",
    features: ["Ecrã AMOLED 6.7\"", "Câmara 108MP", "Bateria 5000mAh", "5G"],
    keywords: ["smartphone 5G", "telemóvel Android", "câmara 108MP"],
    targetAudience: "Utilizadores de tecnologia",
    additionalInfo: "Processador Snapdragon 8 Gen 2, 12GB RAM, 256GB armazenamento. Resistência IP68. Carregamento rápido 67W. Garantia 2 anos."
  },
  {
    name: "Ração Premium para Cães",
    category: "Animais",
    features: ["Sem cereais", "Rico em proteína", "Ingredientes naturais"],
    keywords: ["ração cães premium", "comida cão sem cereais", "ração natural"],
    targetAudience: "Donos de cães",
    additionalInfo: "Composição: Frango desidratado (32%), batata doce, ervilhas, gordura de frango. Rico em ómega 3 e 6. Sem conservantes artificiais. Adequado para cães adultos de todas as raças."
  }
];

async function testModelUpgrade() {
  console.log("🧪 Teste de Upgrade do Modelo OpenAI");
  console.log("=" .repeat(60));
  
  if (!process.env.OPENAI_API_KEY) {
    console.error("❌ Erro: OPENAI_API_KEY não está definida");
    console.log("💡 Configure a variável no arquivo .env.local");
    return;
  }

  const model = process.env.OPENAI_MODEL || "gpt-4.1";
  const fallbackModel = process.env.OPENAI_FALLBACK_MODEL || "gpt-4.1-mini";
  
  console.log("✅ Configuração encontrada:");
  console.log(`🤖 Modelo principal: ${model}`);
  console.log(`🔄 Modelo fallback: ${fallbackModel}`);
  console.log(`📝 Testando ${testProducts.length} produtos...\n`);

  let totalCost = 0;
  let successCount = 0;
  const results = [];

  for (let i = 0; i < testProducts.length; i++) {
    const product = testProducts[i];
    console.log(`🔍 Teste ${i + 1}/${testProducts.length}: ${product.name}`);
    console.log(`📂 Categoria: ${product.category}`);
    
    try {
      const startTime = Date.now();
      
      const response = await fetch('http://localhost:3000/api/generate-description', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'generate',
          productInfo: product
        }),
      });

      const endTime = Date.now();
      const responseTime = endTime - startTime;

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP ${response.status}`);
      }

      const data = await response.json();
      const seoContent = data.seoContent;

      // Validate response structure
      const requiredFields = ['wooCommerceMainDescription', 'wooCommerceShortDescription', 'shortDescription', 'slug'];
      const missingFields = requiredFields.filter(field => !seoContent[field]);
      
      if (missingFields.length > 0) {
        throw new Error(`Campos em falta: ${missingFields.join(', ')}`);
      }

      // Validate SEO description length
      const seoLength = seoContent.shortDescription.length;
      const seoValid = seoLength >= 140 && seoLength <= 160;

      // Validate Portuguese content
      const hasPortugueseChars = /[àáâãçéêíóôõú]/i.test(seoContent.wooCommerceMainDescription);
      
      // Calculate estimated cost (rough estimation)
      const estimatedTokens = JSON.stringify(product).length + seoContent.wooCommerceMainDescription.length;
      const estimatedCost = (estimatedTokens / 1000) * 0.002; // Rough estimate
      totalCost += estimatedCost;

      const result = {
        product: product.name,
        success: true,
        responseTime,
        seoLength,
        seoValid,
        hasPortugueseChars,
        estimatedCost,
        preservationInfo: seoContent.preservationInfo
      };

      results.push(result);
      successCount++;

      console.log(`✅ Sucesso em ${responseTime}ms`);
      console.log(`📏 SEO: ${seoLength} chars ${seoValid ? '✅' : '❌'}`);
      console.log(`🇵🇹 Português: ${hasPortugueseChars ? '✅' : '❌'}`);
      console.log(`💰 Custo estimado: $${estimatedCost.toFixed(4)}`);
      
      if (seoContent.preservationInfo) {
        console.log(`🔍 Preservação: ${seoContent.preservationInfo.preservationRate.toFixed(1)}%`);
      }
      
      console.log("");

    } catch (error) {
      console.error(`❌ Falha: ${error.message}`);
      results.push({
        product: product.name,
        success: false,
        error: error.message
      });
      console.log("");
    }
  }

  // Summary
  console.log("📊 RESUMO DOS TESTES");
  console.log("=" .repeat(60));
  console.log(`✅ Sucessos: ${successCount}/${testProducts.length}`);
  console.log(`💰 Custo total estimado: $${totalCost.toFixed(4)}`);
  console.log(`⏱️  Tempo médio: ${results.filter(r => r.success).reduce((acc, r) => acc + (r.responseTime || 0), 0) / successCount || 0}ms`);
  
  const seoValidCount = results.filter(r => r.seoValid).length;
  const portugueseCount = results.filter(r => r.hasPortugueseChars).length;
  
  console.log(`📏 SEO válido: ${seoValidCount}/${successCount}`);
  console.log(`🇵🇹 Português correto: ${portugueseCount}/${successCount}`);

  if (successCount === testProducts.length) {
    console.log("\n🎉 TODOS OS TESTES PASSARAM!");
    console.log("✅ O upgrade do modelo foi bem-sucedido!");
  } else {
    console.log("\n⚠️  ALGUNS TESTES FALHARAM");
    console.log("🔧 Verifique os erros acima e a configuração");
  }

  return results;
}

// Run tests if called directly
if (require.main === module) {
  testModelUpgrade().catch(console.error);
}

module.exports = { testModelUpgrade };
