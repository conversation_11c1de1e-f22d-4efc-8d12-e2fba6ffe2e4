/**
 * Comprehensive Test Suite for Portuguese Spelling and Grammar Correction
 * 
 * This test validates the automatic Portuguese correction system that:
 * - Corrects spelling and grammar errors in user input
 * - Preserves 100% of technical specifications, numbers, and brand names
 * - Uses Portuguese European standards
 * - Maintains content integrity while improving language quality
 * 
 * Usage: node test-portuguese-correction.js
 * Note: Requires OPENAI_API_KEY environment variable to be set
 */

const testCases = [
  {
    name: "Smartphone com Erros Ortográficos e Técnicos",
    category: "Tecnologia",
    features: ["Ecra OLED", "Camara 108MP", "Bateria 5000mAh"],
    keywords: ["smartphone premium", "telemovel 5g"],
    targetAudience: "Utilizadores de tecnologia",
    additionalInfo: "Processador Snapdragon 8 Gen 2 (4nm), RAM 12GB LPDDR5X, Armazenamento 256GB UFS 4.0. Ecra AMOLED 6.8\" 3200x1440px, 120Hz, 1300 nits, Gorilla Glass Victus 2. Sistema de camaras: Principal 50MP f/1.8 OIS, Ultra-wide 12MP f/2.2 120°, Teleobjetiva 10MP f/2.4 3x zoom optico. Bateria 5000mAh, carregamento rapido 67W, carregamento sem fios 50W. Resistencia IP68, conectividade Wi-Fi 7, Bluetooth 5.3, NFC, USB-C 3.2. Certificaçao militar MIL-STD-810H.",
    expectedCorrections: [
      { field: "features", original: "Ecra", corrected: "Ecrã" },
      { field: "features", original: "Camara", corrected: "Câmara" },
      { field: "keywords", original: "telemovel", corrected: "telemóvel" },
      { field: "additionalInfo", original: "Ecra", corrected: "Ecrã" },
      { field: "additionalInfo", original: "camaras", corrected: "câmaras" },
      { field: "additionalInfo", original: "optico", corrected: "ótico" },
      { field: "additionalInfo", original: "rapido", corrected: "rápido" },
      { field: "additionalInfo", original: "Resistencia", corrected: "Resistência" },
      { field: "additionalInfo", original: "Certificaçao", corrected: "Certificação" }
    ],
    preservedTechnical: [
      "Snapdragon 8 Gen 2 (4nm)", "RAM 12GB LPDDR5X", "256GB UFS 4.0", 
      "AMOLED 6.8\"", "3200x1440px", "120Hz", "1300 nits", "Gorilla Glass Victus 2",
      "50MP f/1.8 OIS", "12MP f/2.2 120°", "10MP f/2.4 3x zoom", "5000mAh",
      "67W", "50W", "IP68", "Wi-Fi 7", "Bluetooth 5.3", "USB-C 3.2", "MIL-STD-810H"
    ]
  },
  {
    name: "Ração com Informações Nutricionais Complexas",
    category: "Animais",
    features: ["Sem cereais", "Rico em proteinas", "Digestao facil"],
    keywords: ["raçao caes premium", "alimentaçao canina"],
    targetAudience: "Donos de caes de raça",
    additionalInfo: "Composiçao: Proteina bruta 32%, Gordura bruta 18%, Fibra bruta 3.5%, Cinza bruta 7.2%, Humidade 10%. Ingredientes principais: Carne de frango desidratada (28%), batata doce, ervilhas, gordura de frango, oleo de salmao (4%), glucosamina (1000mg/kg), condroitina (800mg/kg). Tecnologia de extrusao dupla a baixa temperatura preserva nutrientes. Aprovado pela FEDIAF. Sem conservantes artificiais, corantes ou aromatizantes. Enriquecido com probioticos Lactobacillus acidophilus (1x10^9 UFC/kg). Adequado para caes adultos de todas as raças.",
    expectedCorrections: [
      { field: "features", original: "proteinas", corrected: "proteínas" },
      { field: "features", original: "Digestao", corrected: "Digestão" },
      { field: "features", original: "facil", corrected: "fácil" },
      { field: "keywords", original: "raçao", corrected: "ração" },
      { field: "keywords", original: "caes", corrected: "cães" },
      { field: "keywords", original: "alimentaçao", corrected: "alimentação" },
      { field: "targetAudience", original: "caes", corrected: "cães" },
      { field: "additionalInfo", original: "Composiçao", corrected: "Composição" },
      { field: "additionalInfo", original: "Proteina", corrected: "Proteína" },
      { field: "additionalInfo", original: "oleo", corrected: "óleo" },
      { field: "additionalInfo", original: "salmao", corrected: "salmão" },
      { field: "additionalInfo", original: "extrusao", corrected: "extrusão" },
      { field: "additionalInfo", original: "probioticos", corrected: "probióticos" },
      { field: "additionalInfo", original: "caes", corrected: "cães" }
    ],
    preservedTechnical: [
      "32%", "18%", "3.5%", "7.2%", "10%", "(28%)", "(4%)", 
      "1000mg/kg", "800mg/kg", "FEDIAF", "Lactobacillus acidophilus", "1x10^9 UFC/kg"
    ]
  },
  {
    name: "Azeite com Certificações e Prémios",
    category: "Alimentaçao",
    features: ["Extra virgem", "Primeira extraçao", "Acidez 0.1%"],
    keywords: ["azeite portugues", "azeite premium"],
    targetAudience: "Chefs e gourmets",
    additionalInfo: "Produzido em olivais centenarios da regiao de Tras-os-Montes com mais de 300 anos. Premio Ouro no Concurso Internacional de Azeites de Madrid 2024. Colheita manual realizada exclusivamente durante o mes de outubro. Prensagem a frio em menos de 6 horas apos a colheita. Engarrafado em ambiente controlado com azoto para preservar as propriedades. Certificaçao DOP (Denominaçao de Origem Protegida) e certificaçao biologica pela ECOCERT.",
    expectedCorrections: [
      { field: "category", original: "Alimentaçao", corrected: "Alimentação" },
      { field: "features", original: "extraçao", corrected: "extração" },
      { field: "keywords", original: "portugues", corrected: "português" },
      { field: "additionalInfo", original: "centenarios", corrected: "centenários" },
      { field: "additionalInfo", original: "regiao", corrected: "região" },
      { field: "additionalInfo", original: "Tras-os-Montes", corrected: "Trás-os-Montes" },
      { field: "additionalInfo", original: "Premio", corrected: "Prémio" },
      { field: "additionalInfo", original: "mes", corrected: "mês" },
      { field: "additionalInfo", original: "apos", corrected: "após" },
      { field: "additionalInfo", original: "azoto", corrected: "azoto" }, // This should stay as is (PT-PT)
      { field: "additionalInfo", original: "Certificaçao", corrected: "Certificação" },
      { field: "additionalInfo", original: "Denominaçao", corrected: "Denominação" },
      { field: "additionalInfo", original: "certificaçao", corrected: "certificação" },
      { field: "additionalInfo", original: "biologica", corrected: "biológica" }
    ],
    preservedTechnical: [
      "0.1%", "300 anos", "2024", "6 horas", "DOP", "ECOCERT"
    ]
  },
  {
    name: "Produto com Erros de Concordância de Género",
    category: "Tecnologia",
    features: ["Alta qualidadde", "Resistênte"],
    keywords: ["produto premium"],
    targetAudience: "Profissionais",
    additionalInfo: "Esta produto oferece uma qualidade excepcional. O tecnologia utilizada é de ultima geraçao. Este informaçao é importante para o utilizador. Uma produto como este garante satisfaçao total.",
    expectedCorrections: [
      { field: "features", original: "qualidadde", corrected: "qualidade" },
      { field: "features", original: "Resistênte", corrected: "resistente" },
      { field: "additionalInfo", original: "Esta produto", corrected: "Este produto" },
      { field: "additionalInfo", original: "O tecnologia", corrected: "A tecnologia" },
      { field: "additionalInfo", original: "ultima", corrected: "última" },
      { field: "additionalInfo", original: "geraçao", corrected: "geração" },
      { field: "additionalInfo", original: "Este informaçao", corrected: "Esta informação" },
      { field: "additionalInfo", original: "informaçao", corrected: "informação" },
      { field: "additionalInfo", original: "Uma produto", corrected: "Um produto" },
      { field: "additionalInfo", original: "satisfaçao", corrected: "satisfação" }
    ],
    preservedTechnical: []
  }
];

async function testPortugueseCorrection() {
  console.log("🇵🇹 TESTE ABRANGENTE: Sistema de Correção Ortográfica Portuguesa");
  console.log("=" .repeat(80));
  
  if (!process.env.OPENAI_API_KEY) {
    console.error("❌ Erro: OPENAI_API_KEY não está definida nas variáveis de ambiente");
    return;
  }

  console.log("✅ Chave da OpenAI encontrada");
  console.log(`📝 A testar ${testCases.length} casos de correção ortográfica...\n`);

  const results = [];
  let totalFailures = 0;

  for (let i = 0; i < testCases.length; i++) {
    const testCase = testCases[i];
    console.log(`🧪 Teste ${i + 1}/${testCases.length}: ${testCase.name}`);
    console.log(`📋 Erros esperados: ${testCase.expectedCorrections.length}`);
    console.log(`🔒 Elementos técnicos a preservar: ${testCase.preservedTechnical.length}`);
    
    try {
      const response = await fetch('http://localhost:3000/api/generate-description', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'generate',
          productInfo: testCase
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      const description = data.seoContent.wooCommerceMainDescription;
      
      // Validate technical preservation
      const preservedCount = testCase.preservedTechnical.filter(tech => 
        description.toLowerCase().includes(tech.toLowerCase())
      ).length;
      
      const preservationRate = testCase.preservedTechnical.length > 0 
        ? (preservedCount / testCase.preservedTechnical.length) * 100 
        : 100;

      // Check for original errors (should be corrected)
      const remainingErrors = testCase.expectedCorrections.filter(correction => 
        description.toLowerCase().includes(correction.original.toLowerCase())
      );

      // Check for corrected versions (should be present)
      const appliedCorrections = testCase.expectedCorrections.filter(correction => 
        description.toLowerCase().includes(correction.corrected.toLowerCase())
      );

      const correctionRate = testCase.expectedCorrections.length > 0
        ? (appliedCorrections.length / testCase.expectedCorrections.length) * 100
        : 100;

      const isSuccess = preservationRate >= 95 && correctionRate >= 80 && remainingErrors.length === 0;

      console.log(`📊 Resultados:`);
      console.log(`   Taxa de preservação técnica: ${preservationRate.toFixed(1)}%`);
      console.log(`   Taxa de correção aplicada: ${correctionRate.toFixed(1)}%`);
      console.log(`   Erros remanescentes: ${remainingErrors.length}`);
      console.log(`   Correções aplicadas: ${appliedCorrections.length}/${testCase.expectedCorrections.length}`);
      
      if (isSuccess) {
        console.log(`   ✅ SUCESSO: Correção e preservação adequadas`);
      } else {
        console.log(`   ❌ FALHA: Problemas na correção ou preservação`);
        totalFailures++;
      }

      if (remainingErrors.length > 0) {
        console.log(`   ⚠️  Erros não corrigidos: ${remainingErrors.map(e => e.original).join(', ')}`);
      }

      if (preservationRate < 95) {
        const missingTechnical = testCase.preservedTechnical.filter(tech => 
          !description.toLowerCase().includes(tech.toLowerCase())
        );
        console.log(`   ⚠️  Elementos técnicos perdidos: ${missingTechnical.join(', ')}`);
      }

      results.push({
        testCase: testCase.name,
        preservationRate,
        correctionRate,
        remainingErrors: remainingErrors.length,
        appliedCorrections: appliedCorrections.length,
        isSuccess
      });
      
      console.log("-".repeat(80));
      
    } catch (error) {
      console.error(`❌ Erro no teste ${testCase.name}:`, error.message);
      totalFailures++;
      console.log("-".repeat(80));
    }
    
    // Delay para evitar rate limiting
    if (i < testCases.length - 1) {
      console.log("⏳ Aguardando 3 segundos...\n");
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
  }

  // Relatório final
  console.log("\n📋 RELATÓRIO FINAL DE CORREÇÃO PORTUGUESA");
  console.log("=" .repeat(80));
  
  const successRate = ((results.length - totalFailures) / results.length) * 100;
  const avgPreservationRate = results.reduce((sum, r) => sum + r.preservationRate, 0) / results.length;
  const avgCorrectionRate = results.reduce((sum, r) => sum + r.correctionRate, 0) / results.length;
  const totalRemainingErrors = results.reduce((sum, r) => sum + r.remainingErrors, 0);
  
  console.log(`📊 Estatísticas Gerais:`);
  console.log(`   Taxa de sucesso dos testes: ${successRate.toFixed(1)}%`);
  console.log(`   Taxa média de preservação técnica: ${avgPreservationRate.toFixed(1)}%`);
  console.log(`   Taxa média de correção: ${avgCorrectionRate.toFixed(1)}%`);
  console.log(`   Total de erros não corrigidos: ${totalRemainingErrors}`);
  console.log(`   Testes com falha: ${totalFailures}/${results.length}`);
  
  console.log(`\n🎯 Resultados por Teste:`);
  results.forEach((result, index) => {
    const status = result.isSuccess ? '✅' : '❌';
    console.log(`   ${status} ${result.testCase}: Preservação ${result.preservationRate.toFixed(1)}% | Correção ${result.correctionRate.toFixed(1)}%`);
  });
  
  console.log(`\n📋 Recomendações:`);
  if (successRate < 100) {
    console.log("   ⚠️  CRÍTICO: Alguns testes falharam - revisar sistema de correção");
  }
  if (avgPreservationRate < 95) {
    console.log("   ⚠️  CRÍTICO: Taxa de preservação técnica abaixo do ideal (95%)");
  }
  if (avgCorrectionRate < 80) {
    console.log("   ⚠️  Melhorar taxa de correção ortográfica (objetivo: 80%+)");
  }
  if (totalRemainingErrors === 0 && avgPreservationRate >= 95 && avgCorrectionRate >= 80) {
    console.log("   ✅ EXCELENTE: Sistema de correção funcionando perfeitamente!");
  }
  
  return {
    successRate,
    avgPreservationRate,
    avgCorrectionRate,
    totalRemainingErrors,
    totalFailures,
    results
  };
}

// Executar o teste
if (require.main === module) {
  console.log("🚀 Iniciando teste de correção ortográfica portuguesa...\n");
  testPortugueseCorrection().catch(console.error);
}

module.exports = { 
  testPortugueseCorrection, 
  testCases 
};
