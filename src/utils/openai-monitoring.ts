/**
 * OpenAI Model Performance Monitoring Utilities
 * Tracks model performance, costs, and provides insights for optimization
 */

export interface ModelUsageMetrics {
  model: string;
  timestamp: number;
  promptTokens: number;
  completionTokens: number;
  totalTokens: number;
  cost: number;
  responseTime: number;
  success: boolean;
  errorType?: string;
}

export interface ModelPricing {
  [model: string]: {
    input: number; // Cost per 1M tokens
    output: number; // Cost per 1M tokens
  };
}

// Current OpenAI pricing (as of 2024)
export const MODEL_PRICING: ModelPricing = {
  'gpt-4.1': {
    input: 2.00,
    output: 8.00,
  },
  'gpt-4.1-mini': {
    input: 0.40,
    output: 1.60,
  },
  'gpt-4o': {
    input: 5.00,
    output: 20.00,
  },
  'gpt-4-turbo': {
    input: 10.00,
    output: 30.00,
  },
};

/**
 * Calculate the cost of an OpenAI API call
 */
export function calculateCost(
  model: string,
  promptTokens: number,
  completionTokens: number
): number {
  const pricing = MODEL_PRICING[model];
  if (!pricing) {
    console.warn(`Unknown model pricing for: ${model}`);
    return 0;
  }

  const inputCost = (promptTokens / 1_000_000) * pricing.input;
  const outputCost = (completionTokens / 1_000_000) * pricing.output;
  
  return inputCost + outputCost;
}

/**
 * Log model usage metrics for monitoring
 */
export function logModelUsage(metrics: ModelUsageMetrics): void {
  if (process.env.NODE_ENV === 'development') {
    console.log('📊 Model Usage Metrics:', {
      model: metrics.model,
      cost: `$${metrics.cost.toFixed(4)}`,
      tokens: `${metrics.totalTokens} (${metrics.promptTokens}+${metrics.completionTokens})`,
      responseTime: `${metrics.responseTime}ms`,
      success: metrics.success,
    });
  }

  // In production, you might want to send this to analytics service
  // Example: analytics.track('openai_usage', metrics);
}

/**
 * Get cost comparison between models for the same request
 */
export function getCostComparison(
  promptTokens: number,
  completionTokens: number
): Record<string, number> {
  const comparison: Record<string, number> = {};
  
  Object.keys(MODEL_PRICING).forEach(model => {
    comparison[model] = calculateCost(model, promptTokens, completionTokens);
  });
  
  return comparison;
}

/**
 * Recommend the most cost-effective model based on usage patterns
 */
export function recommendModel(
  averagePromptTokens: number,
  averageCompletionTokens: number,
  qualityRequirement: 'high' | 'medium' | 'low' = 'high'
): string {
  const costs = getCostComparison(averagePromptTokens, averageCompletionTokens);
  
  if (qualityRequirement === 'high') {
    // For high quality requirements, recommend gpt-4.1
    return 'gpt-4.1';
  } else if (qualityRequirement === 'medium') {
    // For medium quality, balance cost and performance
    return 'gpt-4.1-mini';
  } else {
    // For low quality requirements, choose cheapest
    const cheapestModel = Object.entries(costs)
      .sort(([, a], [, b]) => a - b)[0][0];
    return cheapestModel;
  }
}

/**
 * Enhanced error tracking for model failures
 */
export function trackModelError(
  model: string,
  error: any,
  context: string
): void {
  const errorInfo = {
    model,
    error: error.message || 'Unknown error',
    status: error.status || 'unknown',
    context,
    timestamp: Date.now(),
  };

  console.error('🚨 Model Error:', errorInfo);
  
  // In production, send to error tracking service
  // Example: errorTracker.captureException(error, { extra: errorInfo });
}
