/**
 * Portuguese European Spelling and Grammar Correction Service
 * 
 * This service provides automatic correction of Portuguese spelling and grammar errors
 * while preserving 100% content integrity, technical specifications, numbers, and brand names.
 * 
 * Features:
 * - Pre-processes user input before sending to OpenAI API
 * - Corrects common Portuguese spelling and grammar errors
 * - Preserves technical data, percentages, numbers, and brand names
 * - Uses Portuguese European standards (not Brazilian)
 * - Maintains factual content integrity
 */

interface CorrectionResult {
  correctedText: string;
  corrections: Array<{
    original: string;
    corrected: string;
    type: 'spelling' | 'grammar' | 'accent' | 'terminology';
  }>;
  preservedElements: string[];
}

interface ProtectedElement {
  placeholder: string;
  original: string;
  type: 'number' | 'percentage' | 'brand' | 'technical' | 'measurement';
}

/**
 * Comprehensive Portuguese correction service for user input
 */
export class PortugueseCorrectionService {
  private static instance: PortugueseCorrectionService;
  
  // Protected patterns that should never be modified
  private readonly protectedPatterns = [
    // Numbers and percentages
    /\b\d+(?:[.,]\d+)?%?\b/g,
    /\b\d+(?:[.,]\d+)?\s*(?:mg|kg|g|ml|l|cm|mm|m|km|hz|mhz|ghz|w|v|a|mah|ui|mcg|kcal)\b/gi,
    
    // Technical specifications
    /\b(?:IP\d+|USB-?C?|Wi-?Fi\s*\d*|Bluetooth\s*[\d.]*|HDMI|LED|OLED|AMOLED|LCD|4K|8K|HD|FHD|UHD)\b/gi,
    /\b(?:Android|iOS|Windows|macOS|Linux)\s*\d*(?:\.\d+)*/gi,
    /\b(?:RAM|ROM|SSD|HDD|CPU|GPU|DDR\d+|LPDDR\d+|UFS\s*[\d.]*)\b/gi,
    
    // Brand names and models
    /\b(?:Samsung|Apple|iPhone|iPad|Galaxy|Xiaomi|Huawei|OnePlus|Google|Pixel|Sony|LG|Motorola|Nokia)\b/gi,
    /\b(?:Snapdragon|MediaTek|Exynos|A\d+\s*(?:Pro|Bionic)?|Kirin)\s*\d*/gi,
    
    // Certifications and standards
    /\b(?:CE|FCC|ROHS|ISO\s*\d+|DOP|ECOCERT|FEDIAF|GMP|MIL-STD-\d+H?)\b/gi,
    /\b(?:Certificação|Certificado|Aprovado)\s+(?:pela|por|da|do)\s+[A-Z][A-Za-z\s]+/gi,
    
    // Chemical and nutritional terms
    /\b(?:Vitamina\s*[A-Z]\d*|Coenzima\s*Q\d+|Lactobacillus\s+\w+|Omega-?\d+)\b/gi,
    /\b\d+(?:[.,]\d+)?\s*x\s*10\^\d+\s*UFC\/kg\b/gi,
  ];

  // Portuguese European spelling and grammar corrections
  private readonly corrections = {
    // Common spelling errors
    spelling: {
      'qualidadde': 'qualidade',
      'funcionalidadde': 'funcionalidade',
      'resistênte': 'resistente',
      'duravél': 'durável',
      'electrónico': 'eletrónico',
      'electrónicos': 'eletrónicos',
      'electrónica': 'eletrónica',
      'optimizar': 'otimizar',
      'optimizado': 'otimizado',
      'optimização': 'otimização',
      'informaçoes': 'informações',
      'especificaçoes': 'especificações',
      'caracteristicas': 'características',
      'tecnologias': 'tecnologias',
      'funcionalidades': 'funcionalidades',
    },

    // Missing accents
    accents: {
      'facil': 'fácil',
      'util': 'útil',
      'pratico': 'prático',
      'economico': 'económico',
      'tecnologico': 'tecnológico',
      'automatico': 'automático',
      'ergonomico': 'ergonómico',
      'comodo': 'cómodo',
      'rapido': 'rápido',
      'solido': 'sólido',
      'versatil': 'versátil',
      'portatil': 'portátil',
      'flexivel': 'flexível',
      'movel': 'móvel',
      'moveis': 'móveis',
      'faceis': 'fáceis',
      'uteis': 'úteis',
      'praticos': 'práticos',
      'economicos': 'económicos',
      'tecnologicos': 'tecnológicos',
      'automaticos': 'automáticos',
      'ergonomicos': 'ergonómicos',
      'comodos': 'cómodos',
      'rapidos': 'rápidos',
      'solidos': 'sólidos',
      'versateis': 'versáteis',
      'portateis': 'portáteis',
      'flexiveis': 'flexíveis',
    },

    // Gender concordance errors
    grammar: {
      'uma produto': 'um produto',
      'esta produto': 'este produto',
      'essa produto': 'esse produto',
      'aquela produto': 'aquele produto',
      'o qualidade': 'a qualidade',
      'este qualidade': 'esta qualidade',
      'esse qualidade': 'essa qualidade',
      'aquele qualidade': 'aquela qualidade',
      'um tecnologia': 'uma tecnologia',
      'este tecnologia': 'esta tecnologia',
      'esse tecnologia': 'essa tecnologia',
      'aquele tecnologia': 'aquela tecnologia',
      'o informação': 'a informação',
      'este informação': 'esta informação',
      'esse informação': 'essa informação',
      'aquele informação': 'aquela informação',
    },

    // Portuguese vs Brazilian terminology
    terminology: {
      'celular': 'telemóvel',
      'mouse': 'rato',
      'deletar': 'eliminar',
      'salvar': 'guardar',
      'tela': 'ecrã',
      'aplicativo': 'aplicação',
      'aplicativos': 'aplicações',
      'usuário': 'utilizador',
      'usuários': 'utilizadores',
      'ótimo': 'excelente',
      'legal': 'fantástico',
      'bacana': 'interessante',
      'maneiro': 'interessante',
      'top': 'excelente',
      'show': 'fantástico',
    }
  };

  public static getInstance(): PortugueseCorrectionService {
    if (!PortugueseCorrectionService.instance) {
      PortugueseCorrectionService.instance = new PortugueseCorrectionService();
    }
    return PortugueseCorrectionService.instance;
  }

  /**
   * Main correction method for user input
   */
  public correctUserInput(text: string): CorrectionResult {
    if (!text || text.trim() === '') {
      return {
        correctedText: text,
        corrections: [],
        preservedElements: []
      };
    }

    // Step 1: Protect technical elements
    const { protectedText, protectedElements } = this.protectTechnicalElements(text);
    
    // Step 2: Apply corrections
    const { correctedText, corrections } = this.applyCorrections(protectedText);
    
    // Step 3: Restore protected elements
    const finalText = this.restoreProtectedElements(correctedText, protectedElements);
    
    // Step 4: Final cleanup
    const cleanedText = this.finalCleanup(finalText);

    return {
      correctedText: cleanedText,
      corrections,
      preservedElements: protectedElements.map(el => el.original)
    };
  }

  /**
   * Protect technical elements from being modified
   */
  private protectTechnicalElements(text: string): { protectedText: string; protectedElements: ProtectedElement[] } {
    let protectedText = text;
    const protectedElements: ProtectedElement[] = [];
    let placeholderIndex = 0;

    this.protectedPatterns.forEach(pattern => {
      protectedText = protectedText.replace(pattern, (match) => {
        const placeholder = `__PROTECTED_${placeholderIndex++}__`;
        protectedElements.push({
          placeholder,
          original: match,
          type: this.getElementType(match)
        });
        return placeholder;
      });
    });

    return { protectedText, protectedElements };
  }

  /**
   * Determine the type of protected element
   */
  private getElementType(element: string): ProtectedElement['type'] {
    if (/\d+(?:[.,]\d+)?%/.test(element)) return 'percentage';
    if (/\d+(?:[.,]\d+)?\s*(?:mg|kg|g|ml|l|cm|mm|m|km|hz|mhz|ghz|w|v|a|mah|ui|mcg|kcal)/i.test(element)) return 'measurement';
    if (/\d/.test(element)) return 'number';
    if (/^[A-Z]/.test(element)) return 'brand';
    return 'technical';
  }

  /**
   * Apply Portuguese corrections to the text
   */
  private applyCorrections(text: string): { correctedText: string; corrections: Array<{ original: string; corrected: string; type: 'spelling' | 'grammar' | 'accent' | 'terminology' }> } {
    let correctedText = text;
    const corrections: Array<{ original: string; corrected: string; type: 'spelling' | 'grammar' | 'accent' | 'terminology' }> = [];

    // Apply all correction categories
    Object.entries(this.corrections).forEach(([type, correctionMap]) => {
      Object.entries(correctionMap).forEach(([wrong, right]) => {
        const regex = new RegExp(`\\b${this.escapeRegex(wrong)}\\b`, 'gi');
        if (regex.test(correctedText)) {
          correctedText = correctedText.replace(regex, right);
          corrections.push({
            original: wrong,
            corrected: right,
            type: type as 'spelling' | 'grammar' | 'accent' | 'terminology'
          });
        }
      });
    });

    // Fix spacing and punctuation
    correctedText = this.fixSpacingAndPunctuation(correctedText);

    return { correctedText, corrections };
  }

  /**
   * Fix spacing and punctuation issues
   */
  private fixSpacingAndPunctuation(text: string): string {
    let fixed = text;

    // Fix multiple spaces
    fixed = fixed.replace(/\s+/g, ' ');
    
    // Fix spacing around punctuation
    fixed = fixed.replace(/\s+([.,;:!?])/g, '$1');
    fixed = fixed.replace(/([.,;:!?])([a-zA-ZÀ-ÿ])/g, '$1 $2');
    
    // Fix capitalization after punctuation
    fixed = fixed.replace(/([.!?])\s+([a-zà-ÿ])/g, (_, punct, letter) => {
      return punct + ' ' + letter.toUpperCase();
    });

    // Ensure first letter is capitalized
    fixed = fixed.charAt(0).toUpperCase() + fixed.slice(1);

    return fixed;
  }

  /**
   * Restore protected elements
   */
  private restoreProtectedElements(text: string, protectedElements: ProtectedElement[]): string {
    let restoredText = text;
    
    protectedElements.forEach(element => {
      restoredText = restoredText.replace(element.placeholder, element.original);
    });

    return restoredText;
  }

  /**
   * Final cleanup of the text
   */
  private finalCleanup(text: string): string {
    return text
      .replace(/\s+/g, ' ')
      .replace(/\s+$/, '')
      .replace(/^\s+/, '')
      .trim();
  }

  /**
   * Escape special regex characters
   */
  private escapeRegex(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  /**
   * Validate that correction preserved content integrity
   */
  public validateContentIntegrity(original: string, corrected: string): {
    isValid: boolean;
    issues: string[];
    preservationRate: number;
  } {
    const issues: string[] = [];

    // Extract technical elements from both texts
    const originalTechnical = this.extractTechnicalElements(original);
    const correctedTechnical = this.extractTechnicalElements(corrected);

    // Check if all technical elements are preserved
    const missingElements = originalTechnical.filter(element =>
      !correctedTechnical.some(correctedEl =>
        correctedEl.toLowerCase() === element.toLowerCase()
      )
    );

    if (missingElements.length > 0) {
      issues.push(`Missing technical elements: ${missingElements.join(', ')}`);
    }

    // Check for significant length changes (might indicate content loss)
    const lengthDifference = Math.abs(corrected.length - original.length) / original.length;
    if (lengthDifference > 0.2) { // More than 20% change
      issues.push(`Significant length change: ${(lengthDifference * 100).toFixed(1)}%`);
    }

    // Calculate preservation rate
    const preservationRate = originalTechnical.length > 0
      ? ((originalTechnical.length - missingElements.length) / originalTechnical.length) * 100
      : 100;

    return {
      isValid: issues.length === 0 && preservationRate >= 95,
      issues,
      preservationRate
    };
  }

  /**
   * Extract technical elements for validation
   */
  private extractTechnicalElements(text: string): string[] {
    const elements: string[] = [];

    this.protectedPatterns.forEach(pattern => {
      const matches = text.match(pattern);
      if (matches) {
        elements.push(...matches);
      }
    });

    return [...new Set(elements)]; // Remove duplicates
  }
}

// Export singleton instance
export const portugueseCorrectionService = PortugueseCorrectionService.getInstance();
